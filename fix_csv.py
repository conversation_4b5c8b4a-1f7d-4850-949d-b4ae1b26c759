#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix CSV file by ensuring each record is on a single line.
Each record is identified by ending with 'tweet_id:[link]'
"""

import re
import sys

def fix_csv_file(input_file, output_file):
    """
    Fix CSV file by merging multi-line records into single lines.
    Each record ends with 'tweet_id:' followed by a tweet ID.
    """
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # First line is the header
    if not lines:
        return
        
    header = lines[0]
    fixed_lines = [header]
    
    current_record = ""
    
    for i, line in enumerate(lines[1:], 1):
        # Skip empty lines
        if not line.strip():
            continue
            
        # Check if this line ends with tweet_id pattern
        if re.search(r'tweet_id:\d+$', line):
            # This line completes a record
            if current_record:
                # Merge with previous lines and clean up
                full_record = current_record + " " + line
                # Replace multiple spaces with single space and clean up newlines
                full_record = re.sub(r'\s+', ' ', full_record)
                fixed_lines.append(full_record)
            else:
                # This line is a complete record by itself
                cleaned_line = re.sub(r'\s+', ' ', line)
                fixed_lines.append(cleaned_line)
            current_record = ""
        else:
            # This line is part of a multi-line record
            if current_record:
                current_record += " " + line
            else:
                current_record = line
    
    # Handle any remaining incomplete record
    if current_record:
        cleaned_record = re.sub(r'\s+', ' ', current_record)
        fixed_lines.append(cleaned_record)
    
    # Write the fixed content
    with open(output_file, 'w', encoding='utf-8', newline='') as f:
        for line in fixed_lines:
            f.write(line + '\n')
    
    print(f"Fixed CSV file saved as: {output_file}")
    print(f"Original lines: {len(lines)}")
    print(f"Fixed lines: {len(fixed_lines)}")

if __name__ == "__main__":
    input_file = "1-korupsi bansos - Copy.csv"
    output_file = "1-korupsi bansos - Fixed.csv"
    
    try:
        fix_csv_file(input_file, output_file)
        print("CSV file has been successfully fixed!")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
